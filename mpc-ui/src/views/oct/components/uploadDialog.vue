<template>
  <div>
    <el-dialog title="上传" :visible.sync="dialogVisible" :close-on-click-modal="false" width="860px">
      <el-form style="margin-bottom: 22px;" ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="品牌" prop="manufacturerInfoId">
          <el-select style="width: 100%" v-model="formData.manufacturerInfoId" placeholder="请选择品牌" @change="onChange">
            <el-option v-for="item in manufacturerList" :key="item.id" :label="item.manufacturerName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceTypeId">
          <el-select style="width: 100%" v-model="formData.deviceTypeId" placeholder="请选择设备类型" @change="onDeviceTypeChange">
            <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.typeName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件层级规则" v-if="fileRule">
          <span>{{ fileRule }}</span>
        </el-form-item>
        <el-form-item label="文件命名规则" v-if="folderRule">
          <span>{{ folderRule }}</span>
        </el-form-item>
      </el-form>
      <UploadLargeFiles ref="uploader" :formData="formData" v-if="formData.manufacturerInfoId && formData.deviceTypeId" />
      <div slot="footer" class="dialog-footer">
        <div class="tips">
          <span v-if="formData.manufacturerInfoId && formData.deviceTypeId">上传过程中请勿刷新和切换页面，以防数据丢失！！！</span>
        </div>
        <div>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="onSubmit">完 成</el-button>
        </div>
      </div>
    </el-dialog>
    <div class="upload-progress" @click="show" v-if="!dialogVisible && uploadingFileCount">
      <i class="el-icon-upload"></i>
      <el-badge :value="uploadingFileCount">
        上传中...
      </el-badge>
    </div>
  </div>
</template>
<script>
import UploadLargeFiles from './EnhancedFileUploader.vue'
import { getManufacturerInfoAndType } from '@/api/oct/index'
export default {
  name: 'UploadDialog',
  components: { UploadLargeFiles },
  data() {
    return {
      dialogVisible: false,
      formData: {
        deviceTypeId: undefined
      },
      manufacturerList: [],
      deviceTypeList: [],
      rules: {
        manufacturerInfoId: [
          { required: true, message: '请选择品牌', trigger: 'blur' }
        ],
        deviceTypeId: [
          { required: true, message: '请选择设备类型', trigger: 'blur' }
        ]
      },
      fileRule: '',
      folderRule: '',
      uploadingFileCount: 0
    }
  },
  methods: {
    show() {
      this.getManufacturerInfoAndTypeFn()
      this.dialogVisible = true
    },
    getManufacturerInfoAndTypeFn() {
      getManufacturerInfoAndType().then(res => {
        this.manufacturerList = res.data
      })
    },
    onChange(e) {
      this.formData.deviceTypeId = undefined
      this.fileRule = ''
      this.folderRule = ''
      this.deviceTypeList = this.manufacturerList.find(item => item.id === e)?.children
    },
    onDeviceTypeChange(e) {
      this.fileRule = ''
      this.folderRule = ''
      let obj = this.deviceTypeList.find(item => item.id === e)
      if (!obj) return
      this.fileRule = obj.folderRule?.remark 
      this.folderRule = obj.fileRule?.remark
    },
    onSubmit() {
      if (this.$refs.uploader) {
        if (this.$refs.uploader.uploadingFileCount > 0 || this.$refs.uploader.pausedFileCount > 0) {
          return this.$message.error('请等待文件上传完成')
        }
      }
      this.dialogVisible = false
      this.formData = {}
      this.fileRule = ''
      this.folderRule = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .tips {
    font-size: 14px;
    color: #f56c6c;
  }
}
.upload-progress {
  position: fixed;
  bottom: 100px;
  right: 20px;
  padding: 8px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 0 10px #ccc;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #409eff;
  cursor: pointer;
  user-select: none;

  .el-icon-upload {
    color: #409eff;
    font-size: 20px;
    margin-right: 4px;
  }
}
</style>