<template>
  <div class="tree-node">
    <div
      class="node-content"
      :class="{ 'is-folder': node.type === 'folder' }"
    >
      <!-- 文件名区域（固定宽度，内部处理缩进） -->
      <div class="name-section">
        <!-- 缩进空间 -->
        <span class="indent-space" :style="{ width: (level * 20) + 'px' }"></span>

        <!-- 文件夹展开/收起图标 -->
        <i
          v-if="node.type === 'folder'"
          class="folder-toggle"
          :class="node.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
          @click="$emit('toggle-folder', fullPath)"
        ></i>
        <span v-else class="file-indent"></span>

        <!-- 文件/文件夹图标 -->
        <i :class="getFileIcon(node.name, node.type)" class="node-icon"></i>

        <!-- 文件/文件夹名称 -->
        <span class="node-name" :title="node.name">{{ node.name }}</span>
      </div>

      <!-- 文件信息（仅文件显示） -->
      <div v-if="node.type === 'file'" class="file-info">
        <span class="file-size">{{ formatFileSize(node.file && node.file.size) }}</span>

        <!-- 进度条 -->
        <div class="progress-container">
          <el-progress
            :percentage="node.progress || 0"
            :status="node.status === 'error' ? 'exception' : (node.status === 'success' ? 'success' : null)"
            :stroke-width="6"
            :show-text="false"
          ></el-progress>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
          <span
            class="status-text"
            :style="{ color: getStatusColor(node.status) }"
            :title="getStatusText(node)"
          >
            {{ getStatusText(node) }}
          </span>
        </div>

        <!-- 操作按钮 -->
        <div class="file-actions">
          <!-- 暂停按钮 -->
          <button
            v-if="node.status === 'uploading'"
            class="action-btn pause-btn"
            @click="$emit('pause-file', node.file)"
          >
            暂停
          </button>

          <!-- 继续按钮 -->
          <button
            v-if="node.status === 'paused'"
            class="action-btn resume-btn"
            @click="$emit('resume-file', node.file)"
          >
            继续
          </button>

          <!-- 重试按钮 -->
          <button
            v-if="node.status === 'error' && node.statusText !== '文件已存在'"
            class="action-btn retry-btn"
            @click="$emit('retry-file', node.file)"
          >
            重试
          </button>

          <!-- 删除按钮 -->
          <button
            v-if="!node.md5Computing && node.status !== 'uploading'"
            class="action-btn delete-btn"
            @click="$emit('remove-file', node.file)"
          >
            删除
          </button>
        </div>
      </div>


    </div>

    <!-- 子节点（文件夹内容） -->
    <template v-if="node.type === 'folder' && node.expanded && node.children">
      <file-tree-node
        v-for="(childNode, childKey) in node.children"
        :key="childKey"
        :node="childNode"
        :node-key="childKey"
        :level="level + 1"
        :parent-path="fullPath"
        @toggle-folder="$emit('toggle-folder', $event)"
        @remove-file="$emit('remove-file', $event)"
        @pause-file="$emit('pause-file', $event)"
        @resume-file="$emit('resume-file', $event)"
        @retry-file="$emit('retry-file', $event)"
      />
    </template>
  </div>
</template>

<script>
export default {
  name: 'FileTreeNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    nodeKey: {
      type: String,
      required: true
    },
    level: {
      type: Number,
      default: 0
    },
    parentPath: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileIcons: {
        folder: 'el-icon-folder',
        zip: 'el-icon-box',
        rar: 'el-icon-box',
        '7z': 'el-icon-box',
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        txt: 'el-icon-document',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mp3: 'el-icon-headset',
        wav: 'el-icon-headset',
        default: 'el-icon-document'
      }
    }
  },
  computed: {
    // 计算完整路径
    fullPath() {
      return this.parentPath ? `${this.parentPath}/${this.nodeKey}` : this.nodeKey
    }
  },
  methods: {
    getFileIcon(name, type) {
      if (type === 'folder') return this.fileIcons.folder;
      const parts = name.split('.');
      const ext = parts.length > 1 ? parts.pop().toLowerCase() : '';
      return this.fileIcons[ext] || this.fileIcons.default;
    },
    getStatusColor(status) {
      const colors = {
        success: '#67C23A',
        error: '#F56C6C',
        uploading: '#409EFF',
        paused: '#E6A23C',
        waiting: '#909399'
      };
      return colors[status] || colors.waiting;
    },
    formatFileSize(size) {
      if (!size) return '';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(1)} ${units[index]}`;
    },

    getStatusText(node) {
      if (node.status === 'uploading') {
        let text = `${node.progress || 0}%`;

        // 调试日志（生产环境可移除）
        // console.log('Status:', node.name, text);

        // 添加网速信息
        if (node.speed && node.speed.trim() !== '') {
          text += ` ${node.speed}`;
        }

        // 添加预计时间（简化文本）
        if (node.timeRemaining && node.timeRemaining.trim() !== '') {
          text += ` 剩余${node.timeRemaining}`;
        }

        return text;
      }

      // 错误状态显示详细信息
      if (node.status === 'error') {
        // 优先使用 statusText，如果没有则使用默认文本
        if (node.statusText) {
          return node.statusText;
        }
        let text = '上传失败';
        if (node.errorDetail) {
          text += `：${node.errorDetail}`;
        }
        return text;
      }

      // 其他状态的文本
      const statusTexts = {
        waiting: '等待上传',
        success: '上传完成',
        paused: '已暂停',
        computing: '计算MD5中...'
      };

      return node.statusText || statusTexts[node.status] || '等待上传';
    },

    formatSpeed(bytesPerSecond) {
      if (!bytesPerSecond || isNaN(bytesPerSecond) || bytesPerSecond <= 0) return '';

      const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
      let speed = bytesPerSecond;
      let index = 0;

      while (speed >= 1024 && index < units.length - 1) {
        speed /= 1024;
        index++;
      }

      return `${speed.toFixed(1)} ${units[index]}`;
    },

    formatTimeRemaining(seconds) {
      if (!seconds || isNaN(seconds) || seconds <= 0 || !isFinite(seconds)) return '';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-node {
  .node-content {
    display: flex;
    align-items: center;
    padding: 8px 10px; // 左右添加 padding 与表头对齐
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    min-width: 750px; // 增加最小宽度以容纳更宽的状态列
    width: 100%;

    &:hover {
      background-color: #f5f5f5;
    }

    .name-section {
      flex: 0 0 220px; // 固定宽度与表头对齐
      display: flex;
      align-items: center;
      margin-right: 12px;
      overflow: hidden;

      .indent-space {
        flex-shrink: 0; // 不收缩
      }
    }
  }

    &.is-folder {
      font-weight: 500;
    }

    .folder-toggle {
      margin-right: 8px;
      cursor: pointer;
      color: #666;
      transition: transform 0.2s ease;

      &:hover {
        color: #1890ff;
      }
    }

    .file-indent {
      width: 16px;
      margin-right: 8px;
    }

    .node-icon {
      margin-right: 8px;
      font-size: 16px;

      &.el-icon-folder {
        color: #faad14;
      }

      &.el-icon-box {
        color: #722ed1;
      }

      &.el-icon-document {
        color: #1890ff;
      }

      &.el-icon-picture {
        color: #52c41a;
      }

      &.el-icon-video-camera {
        color: #f5222d;
      }

      &.el-icon-headset {
        color: #13c2c2;
      }
    }

    .node-name {
      flex: 1; // 在 name-section 内占据剩余空间
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0; // 允许收缩

      .file-size {
        flex: 0 0 80px; // 固定宽度
        font-size: 12px;
        color: #666;
        margin-right: 12px;
      }

      .progress-container {
        flex: 0 0 140px; // 固定宽度
        margin-right: 12px;
      }

      .status-info {
        flex: 0 0 220px; // 增加宽度与表头保持一致
        margin-right: 12px;
        overflow: hidden;

        .status-text {
          font-size: 12px;
          line-height: 1.4;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .file-actions {
        flex: 1; // 占据剩余空间，与表头一致
        display: flex;
        gap: 6px;
        align-items: center;
        // justify-content: flex-end; // 右对齐

        .action-btn {
          padding: 4px 8px;
          font-size: 12px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s ease;
          background: transparent;
          color: #666;

          &:hover {
            background-color: #f5f5f5;
            color: #333;
          }

          &:active {
            transform: translateY(1px);
          }
        }

        .pause-btn {
          color: #e6a23c;

          &:hover {
            background-color: #fdf6ec;
            color: #cf9236;
          }
        }

        .resume-btn {
          color: #67c23a;

          &:hover {
            background-color: #f0f9ff;
            color: #5daf34;
          }
        }

        .retry-btn {
          color: #409eff;

          &:hover {
            background-color: #ecf5ff;
            color: #3a8ee6;
          }
        }

        .delete-btn {
          color: #f56c6c;

          &:hover {
            background-color: #fef0f0;
            color: #dd6161;
          }
        }
      }
    }
  }

// 响应式设计
@media (max-width: 768px) {
  .tree-node .node-content {
    flex-direction: column;
    align-items: flex-start;

    .file-info {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      margin-top: 8px;

      .progress-container {
        width: 100%;
        margin: 4px 0;
      }

      .file-actions {
        margin-top: 8px;
      }
    }
  }
}
</style>
