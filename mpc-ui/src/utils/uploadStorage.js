/**
 * 上传任务持久化存储工具
 * 使用IndexedDB存储上传状态和File对象，支持页面刷新后恢复上传任务
 */

class UploadStorage {
  constructor() {
    this.dbName = 'UploadTasksDB'
    this.dbVersion = 1
    this.storeName = 'uploadTasks'
    this.db = null
  }

  /**
   * 初始化IndexedDB
   */
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)
      
      request.onerror = () => {
        console.error('IndexedDB打开失败:', request.error)
        reject(request.error)
      }
      
      request.onsuccess = () => {
        this.db = request.result
        resolve(this.db)
      }
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        
        // 创建对象存储
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'id' })
          store.createIndex('sessionId', 'sessionId', { unique: false })
          store.createIndex('status', 'status', { unique: false })
        }
      }
    })
  }

  /**
   * 保存上传任务
   */
  async saveUploadTask(taskData) {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      
      const request = store.put(taskData)
      
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 获取所有未完成的上传任务
   */
  async getUnfinishedTasks(sessionId) {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('sessionId')
      
      const request = index.getAll(sessionId)
      
      request.onsuccess = () => {
        const tasks = request.result.filter(task => 
          task.status !== 'success' && task.status !== 'cancelled'
        )
        resolve(tasks)
      }
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 更新上传任务状态
   */
  async updateTaskStatus(taskId, updates) {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      
      const getRequest = store.get(taskId)
      
      getRequest.onsuccess = () => {
        const task = getRequest.result
        if (task) {
          Object.assign(task, updates, { updatedAt: Date.now() })
          
          const putRequest = store.put(task)
          putRequest.onsuccess = () => resolve(task)
          putRequest.onerror = () => reject(putRequest.error)
        } else {
          reject(new Error('Task not found'))
        }
      }
      
      getRequest.onerror = () => reject(getRequest.error)
    })
  }

  /**
   * 删除上传任务
   */
  async deleteTask(taskId) {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      
      const request = store.delete(taskId)
      
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 清理已完成的任务
   */
  async cleanupCompletedTasks(sessionId) {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('sessionId')
      
      const request = index.openCursor(sessionId)
      
      request.onsuccess = (event) => {
        const cursor = event.target.result
        if (cursor) {
          const task = cursor.value
          if (task.status === 'success' || task.status === 'cancelled') {
            cursor.delete()
          }
          cursor.continue()
        } else {
          resolve()
        }
      }
      
      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 获取会话ID（用于区分不同的上传会话）
   */
  getSessionId() {
    let sessionId = sessionStorage.getItem('uploadSessionId')
    if (!sessionId) {
      sessionId = 'upload_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      sessionStorage.setItem('uploadSessionId', sessionId)
    }
    return sessionId
  }

  /**
   * 创建任务数据结构
   */
  createTaskData(file, formData, additionalData = {}) {
    return {
      id: this.generateTaskId(file),
      sessionId: this.getSessionId(),
      file: file, // IndexedDB可以直接存储File对象
      fileName: file.name,
      fileSize: file.size,
      relativePath: file.relativePath || file.name,
      lastModified: file.lastModified,
      formData: formData,
      status: 'waiting',
      progress: 0,
      md5: null,
      md5Computing: false,
      statusText: '等待上传',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      ...additionalData
    }
  }

  /**
   * 生成任务ID
   */
  generateTaskId(file) {
    return `task_${file.name}_${file.size}_${file.lastModified}`
  }

  /**
   * 检查IndexedDB是否可用
   */
  static isSupported() {
    return 'indexedDB' in window
  }
}

// 创建单例实例
const uploadStorage = new UploadStorage()

export default uploadStorage
